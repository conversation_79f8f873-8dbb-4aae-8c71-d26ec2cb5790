import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON>pography,
  Button,
  Alert,
  CircularProgress,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  InputAdornment,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  GetApp as ExportIcon,
  Assignment as ReportIcon,
  Security as SecurityIcon,
  Warning as IncidentIcon,
  Schedule as ScheduleIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  LocationOn as LocationIcon,
  Person as PersonIcon,
  AccessTime as TimeIcon,
  CheckCircle as ApprovedIcon,
  Cancel as RejectedIcon,
  HourglassEmpty as PendingIcon,
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { isAuthenticationAvailable, getCurrentTokenInfo, clientAPI } from '../services/api';

interface Report {
  id: string;
  type: 'INCIDENT' | 'PATROL' | 'MAINTENANCE' | 'SECURITY' | 'GENERAL';
  title: string;
  description: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status: 'DRAFT' | 'SUBMITTED' | 'REVIEWED' | 'APPROVED' | 'REJECTED';
  agentId: string;
  siteId: string;
  location?: string;
  timestamp: string;
  submittedAt?: string;
  reviewedAt?: string;
  reviewedBy?: string;
  attachments: ReportAttachment[];
  agent: {
    id: string;
    employeeId: string;
    user: {
      firstName: string;
      lastName: string;
    };
  };
  site: {
    id: string;
    name: string;
    address: any;
  };
}

interface ReportAttachment {
  id: string;
  type: 'PHOTO' | 'VIDEO' | 'DOCUMENT' | 'AUDIO';
  filename: string;
  url: string;
  size: number;
  uploadedAt: string;
}

interface ReportStats {
  totalReports: number;
  todayReports: number;
  pendingReports: number;
  criticalReports: number;
  incidentReports: number;
  patrolReports: number;
  averageResponseTime: number;
  reportsByType: { type: string; count: number }[];
}

const ReportsPage: React.FC = () => {
  
  // State management
  const [reports, setReports] = useState<Report[]>([]);
  const [stats, setStats] = useState<ReportStats | null>(null);
  const [sites, setSites] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [filterSite, setFilterSite] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Data fetching functions
  const fetchSites = useCallback(async () => {
    try {
      const response = await clientAPI.getSites();
      setSites(response.data || []);
    } catch (err) {
      console.error('Failed to fetch sites:', err);
      // Don't set error for sites as it's not critical
    }
  }, []);

  const fetchReports = useCallback(async () => {
    // Check authentication availability first
    const authAvailable = await isAuthenticationAvailable();
    if (!authAvailable) {
      setError('Authentication not available. Please log in.');
      setLoading(false);
      return;
    }

    try {
      setError(null);
      setLoading(true);

      // Get current token info for debugging
      const tokenInfo = await getCurrentTokenInfo();
      console.debug(`Loading client reports with ${tokenInfo?.type || 'unknown'} token`);

      const params = {
        page: page.toString(),
        limit: rowsPerPage.toString(),
        type: filterType !== 'all' ? filterType : undefined,
        status: filterStatus !== 'all' ? filterStatus : undefined,
        priority: filterPriority !== 'all' ? filterPriority : undefined,
        site: filterSite !== 'all' ? filterSite : undefined,
        search: searchQuery || undefined,
      };

      // Use the enhanced client API service
      const reportsResponse = await clientAPI.getReports(params);
      
      // Calculate real stats from the reports data
      const reports = reportsResponse.data || [];
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const todayReports = reports.filter((report: any) => {
        const reportDate = new Date(report.createdAt);
        reportDate.setHours(0, 0, 0, 0);
        return reportDate.getTime() === today.getTime();
      }).length;

      const pendingReports = reports.filter((report: any) =>
        report.status === 'PENDING' || report.status === 'SUBMITTED'
      ).length;

      const criticalReports = reports.filter((report: any) =>
        report.priority === 'CRITICAL' || report.priority === 'HIGH'
      ).length;

      const incidentReports = reports.filter((report: any) =>
        report.type === 'INCIDENT'
      ).length;

      const patrolReports = reports.filter((report: any) =>
        report.type === 'PATROL'
      ).length;

      // Calculate average response time for resolved reports
      const resolvedReports = reports.filter((report: any) =>
        report.status === 'RESOLVED' && report.resolvedAt
      );

      const averageResponseTime = resolvedReports.length > 0
        ? resolvedReports.reduce((sum: number, report: any) => {
            const created = new Date(report.createdAt).getTime();
            const resolved = new Date(report.resolvedAt).getTime();
            return sum + (resolved - created);
          }, 0) / resolvedReports.length / (1000 * 60 * 60) // Convert to hours
        : 0;

      // Group reports by type
      const reportsByType = reports.reduce((acc: any, report: any) => {
        const type = report.type || 'OTHER';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {});

      const realStats = {
        totalReports: reports.length,
        todayReports,
        pendingReports,
        criticalReports,
        incidentReports,
        patrolReports,
        averageResponseTime: Math.round(averageResponseTime * 10) / 10,
        reportsByType: Object.entries(reportsByType).map(([type, count]) => ({
          type,
          count
        })),
      };

      setReports(reports);
      setStats(realStats);
      setLastUpdated(new Date());

    } catch (err: any) {
      console.error('Failed to fetch reports:', err);
      setError('Failed to load reports data. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, filterType, filterStatus, filterPriority, filterSite, searchQuery]);

  const exportReports = async () => {
    // Check authentication availability first
    const authAvailable = await isAuthenticationAvailable();
    if (!authAvailable) {
      setError('Authentication required to export reports');
      return;
    }

    try {
      // Get current token info for debugging
      const tokenInfo = await getCurrentTokenInfo();
      console.debug(`Exporting reports with ${tokenInfo?.type || 'unknown'} token`);

      // Use the real API endpoint for PDF export
      try {
        const response = await clientPortalAPI.getReports({
          format: 'pdf',
          type: filterType,
          status: filterStatus,
          priority: filterPriority,
          site: filterSite,
          search: searchQuery
        });

        // Handle the blob response
        const blob = new Blob([response.data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `security-reports-${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } catch (exportError) {
        console.warn('PDF export endpoint not available, generating CSV instead');

        // Fallback to CSV export using current reports data
        const csvContent = generateCSVFromReports(reports);
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `security-reports-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }

    } catch (err: any) {
      console.error('Failed to export reports:', err);
      setError('Failed to export reports. Please try again.');
    }
  };

  // CSV generation utility
  const generateCSVFromReports = (reportsData: any[]) => {
    const headers = ['ID', 'Type', 'Title', 'Status', 'Priority', 'Site', 'Agent', 'Created At', 'Updated At'];
    const csvRows = [headers.join(',')];

    reportsData.forEach(report => {
      const row = [
        report.id || '',
        report.type || '',
        `"${(report.title || '').replace(/"/g, '""')}"`,
        report.status || '',
        report.priority || '',
        `"${(report.siteName || '').replace(/"/g, '""')}"`,
        `"${(report.agentName || '').replace(/"/g, '""')}"`,
        report.createdAt || '',
        report.updatedAt || ''
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  };

  // Utility functions
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'INCIDENT':
        return <IncidentIcon color="error" />;
      case 'PATROL':
        return <SecurityIcon color="primary" />;
      case 'MAINTENANCE':
        return <ScheduleIcon color="warning" />;
      case 'SECURITY':
        return <SecurityIcon color="info" />;
      default:
        return <ReportIcon />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'success';
      case 'SUBMITTED':
        return 'info';
      case 'REVIEWED':
        return 'warning';
      case 'REJECTED':
        return 'error';
      case 'DRAFT':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'CRITICAL':
        return 'error';
      case 'HIGH':
        return 'warning';
      case 'MEDIUM':
        return 'info';
      case 'LOW':
        return 'success';
      default:
        return 'default';
    }
  };

  const formatTimestamp = (timestamp: string | undefined | null) => {
    if (!timestamp) return 'N/A';
    try {
      const date = new Date(timestamp);
      return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleString();
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Effects
  useEffect(() => {
    fetchSites();
    fetchReports();
  }, [fetchSites, fetchReports]);

  // Loading state
  if (loading && reports.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
        <Box textAlign="center">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading Reports...
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Security Reports
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Comprehensive incident and patrol reporting system
          </Typography>
        </Box>
        <Box display="flex" gap={2} alignItems="center">
          {lastUpdated && (
            <Typography variant="caption" color="text.secondary">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </Typography>
          )}
          <Button
            variant="outlined"
            onClick={exportReports}
            startIcon={<ExportIcon />}
          >
            Export
          </Button>
          <Button
            variant="outlined"
            onClick={fetchReports}
            startIcon={<RefreshIcon />}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Stats Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <ReportIcon color="primary" />
                  <Box>
                    <Typography variant="h6">{stats.totalReports}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Reports
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <ScheduleIcon color="info" />
                  <Box>
                    <Typography variant="h6">{stats.todayReports}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Today's Reports
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <IncidentIcon color="warning" />
                  <Box>
                    <Typography variant="h6">{stats.pendingReports}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pending Review
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <SecurityIcon color="error" />
                  <Box>
                    <Typography variant="h6">{stats.criticalReports}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Critical Reports
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <FilterIcon color="primary" />
            <Typography variant="h6">Filters</Typography>
          </Box>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Search reports"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                placeholder="Search by title, description, or agent..."
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={filterType}
                  label="Type"
                  onChange={(e) => setFilterType(e.target.value)}
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="INCIDENT">Incident</MenuItem>
                  <MenuItem value="PATROL">Patrol</MenuItem>
                  <MenuItem value="MAINTENANCE">Maintenance</MenuItem>
                  <MenuItem value="SECURITY">Security</MenuItem>
                  <MenuItem value="GENERAL">General</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filterStatus}
                  label="Status"
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="DRAFT">Draft</MenuItem>
                  <MenuItem value="SUBMITTED">Submitted</MenuItem>
                  <MenuItem value="REVIEWED">Reviewed</MenuItem>
                  <MenuItem value="APPROVED">Approved</MenuItem>
                  <MenuItem value="REJECTED">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={filterPriority}
                  label="Priority"
                  onChange={(e) => setFilterPriority(e.target.value)}
                >
                  <MenuItem value="all">All Priority</MenuItem>
                  <MenuItem value="LOW">Low</MenuItem>
                  <MenuItem value="MEDIUM">Medium</MenuItem>
                  <MenuItem value="HIGH">High</MenuItem>
                  <MenuItem value="CRITICAL">Critical</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Site</InputLabel>
                <Select
                  value={filterSite}
                  label="Site"
                  onChange={(e) => setFilterSite(e.target.value)}
                >
                  <MenuItem value="all">All Sites</MenuItem>
                  {sites.map((site) => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Reports Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Reports</Typography>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                {reports.length} reports found
              </Typography>
              <Button
                variant="outlined"
                startIcon={<ExportIcon />}
                onClick={exportReports}
                disabled={loading || reports.length === 0}
                size="small"
              >
                Export
              </Button>
            </Box>
          </Box>

          {loading && reports.length === 0 ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : reports.length === 0 ? (
            <Box textAlign="center" py={4}>
              <ReportIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                No reports found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Try adjusting your filters or search criteria
              </Typography>
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Type</TableCell>
                      <TableCell>Title</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Priority</TableCell>
                      <TableCell>Agent</TableCell>
                      <TableCell>Site</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {reports.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((report) => (
                      <TableRow key={report.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <ReportIcon color="primary" fontSize="small" />
                            <Typography variant="body2">
                              {report.type}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {report.title}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {report.description.substring(0, 50)}...
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={report.status}
                            size="small"
                            color={
                              report.status === 'APPROVED' ? 'success' :
                              report.status === 'REJECTED' ? 'error' :
                              report.status === 'REVIEWED' ? 'info' :
                              report.status === 'SUBMITTED' ? 'warning' : 'default'
                            }
                            icon={
                              report.status === 'APPROVED' ? <ApprovedIcon /> :
                              report.status === 'REJECTED' ? <RejectedIcon /> :
                              report.status === 'SUBMITTED' || report.status === 'REVIEWED' ? <PendingIcon /> : undefined
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={report.priority}
                            size="small"
                            color={
                              report.priority === 'CRITICAL' ? 'error' :
                              report.priority === 'HIGH' ? 'warning' :
                              report.priority === 'MEDIUM' ? 'info' : 'default'
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <PersonIcon fontSize="small" color="action" />
                            <Typography variant="body2">
                              {report.agent.user.firstName} {report.agent.user.lastName}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LocationIcon fontSize="small" color="action" />
                            <Typography variant="body2">
                              {report.site.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <TimeIcon fontSize="small" color="action" />
                            <Typography variant="body2">
                              {new Date(report.timestamp).toLocaleDateString()}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="center">
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Tooltip title="View Report">
                              <IconButton
                                size="small"
                                onClick={() => {
                                  // TODO: Implement report details dialog
                                  console.log('View report:', report.id);
                                }}
                              >
                                <ViewIcon />
                              </IconButton>
                            </Tooltip>
                            {(report.status === 'DRAFT' || report.status === 'SUBMITTED') && (
                              <Tooltip title="Edit Report">
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    // TODO: Implement report editing
                                    console.log('Edit report:', report.id);
                                  }}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={reports.length}
                page={page}
                onPageChange={(_, newPage) => setPage(newPage)}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={(e) => {
                  setRowsPerPage(parseInt(e.target.value, 10));
                  setPage(0);
                }}
                rowsPerPageOptions={[5, 10, 25, 50]}
              />
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default ReportsPage;
