{"name": "bahinlink-backend", "version": "1.0.0", "description": "BahinLink Security Workforce Management Backend API", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "build:watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config jest.integration.config.js", "test:e2e": "jest --config jest.e2e.config.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "prisma db seed", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "docker:build": "docker build -t bahinlink-backend .", "docker:run": "docker run -p 8000:8000 bahinlink-backend", "prepare": "husky install"}, "keywords": ["security", "workforce", "management", "tracking", "api", "nodejs", "typescript"], "author": "BahinLink Team", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "^3.462.0", "@aws-sdk/s3-request-presigner": "^3.462.0", "@clerk/backend": "^2.1.0", "@prisma/client": "^5.22.0", "@types/pdfkit": "^0.17.0", "apn": "^2.2.0", "aws-sdk": "^2.1509.0", "bull": "^4.12.2", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^3.1.6", "cross-env": "^7.0.3", "crypto": "^1.0.1", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "firebase-admin": "^11.11.1", "geolib": "^3.3.4", "google-maps": "^4.3.3", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pdfkit": "^0.17.1", "redis": "^4.6.11", "sharp": "^0.33.1", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "twilio": "^4.19.0", "uuid": "^9.0.1", "web-push": "^3.6.7", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jest": "^29.5.8", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.19.1", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@types/web-push": "^3.6.4", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^8.0.0", "jest": "^29.7.0", "lint-staged": "^15.2.0", "nodemon": "^3.1.10", "prettier": "^3.1.0", "prisma": "^5.22.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "prisma": {"seed": "ts-node prisma/seed-dashboard.ts"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}